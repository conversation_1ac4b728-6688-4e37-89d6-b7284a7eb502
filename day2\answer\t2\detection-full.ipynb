{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "from PIL import Image\n", "import sys\n", "import os\n", "import numpy as np\n", "from pathlib import Path\n", "from typing import Union\n", "import cv2\n", "from ultralytics import YOLO"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def train():\n", "    # 使用预训练模型进行训练\n", "    model = YOLO('weights/yolov8s.pt')\n", "\n", "    # 使用medicine数据集训练模型\n", "    model.train(data=\"medicine.yaml\", epochs=100, imgsz=640)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def test_img():\n", "\t# 训练好的模型权重路径\n", "    model = YOLO(\"runs/detect/train/weights/best.pt\")\n", "    # 测试图片的路径\n", "    img = cv2.imread(\"test.jpg\")\n", "    res = model(img)\n", "    ann = res[0].plot()\n", "    while True:\n", "        cv2.imshow(\"yolo\", ann)\n", "        if cv2.wait<PERSON><PERSON>(1) & 0xFF == ord('q'):\n", "            break\n", "    # 设置保存图片的路径\n", "    cur_path = sys.path[0]\n", "    print(cur_path, sys.path)\n", "\n", "    if os.path.exists(cur_path):\n", "        cv2.imwrite(cur_path + \"out.jpg\", ann)\n", "    else:\n", "        os.mkdir(cur_path)\n", "        cv2.imwrite(cur_path + \"out.jpg\", ann)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def predict():\n", "    # Load a model\n", "    # model = YOLO('yolov8n.pt')  # 加载官方的模型权重作评估\n", "    model = YOLO('runs/detect/train/weights/best.pt')  # 加载自定义的模型权重作评估\n", "\n", "    # 评估\n", "    metrics = model.val()\n", "    # 如果要在新的数据集上测试训练结果，需要将数据集绝对路径传入，例如：\n", "    # metrics = model.val(data=“YOLOv8/.../VOC.yaml”)\n", "    print(metrics.box.map)  # map50-95\n", "    print(metrics.box.map50)  # map50\n", "    print(metrics.box.map75)  # map75\n", "    print(metrics.box.maps)  # 包含每个类别的map50-95列表\n", "\n", "    # Accessing AP75 for each category\n", "    ap75_each_category = metrics.box.maps[:, 5]  # 利用maps矩阵可以得到AP75\n", "    print(ap75_each_category)\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["New https://pypi.org/project/ultralytics/8.3.179 available  Update with 'pip install -U ultralytics'\n", "Ultralytics 8.3.170  Python-3.8.20 torch-2.4.1+cpu CPU (AMD Ryzen 5 7500F 6-Core Processor)\n", "\u001b[34m\u001b[1mengine\\trainer: \u001b[0magnostic_nms=False, amp=True, augment=False, auto_augment=randaugment, batch=16, bgr=0.0, box=7.5, cache=False, cfg=None, classes=None, close_mosaic=10, cls=0.5, conf=None, copy_paste=0.0, copy_paste_mode=flip, cos_lr=False, cutmix=0.0, data=medicine.yaml, degrees=0.0, deterministic=True, device=cpu, dfl=1.5, dnn=False, dropout=0.0, dynamic=False, embed=None, epochs=100, erasing=0.4, exist_ok=False, fliplr=0.5, flipud=0.0, format=torchscript, fraction=1.0, freeze=None, half=False, hsv_h=0.015, hsv_s=0.7, hsv_v=0.4, imgsz=640, int8=False, iou=0.7, keras=False, kobj=1.0, line_width=None, lr0=0.01, lrf=0.01, mask_ratio=4, max_det=300, mixup=0.0, mode=train, model=weights/yolov8s.pt, momentum=0.937, mosaic=1.0, multi_scale=False, name=train2, nbs=64, nms=False, opset=None, optimize=False, optimizer=auto, overlap_mask=True, patience=100, perspective=0.0, plots=True, pose=12.0, pretrained=True, profile=False, project=None, rect=False, resume=False, retina_masks=False, save=True, save_conf=False, save_crop=False, save_dir=runs\\detect\\train2, save_frames=False, save_json=False, save_period=-1, save_txt=False, scale=0.5, seed=0, shear=0.0, show=False, show_boxes=True, show_conf=True, show_labels=True, simplify=True, single_cls=False, source=None, split=val, stream_buffer=False, task=detect, time=None, tracker=botsort.yaml, translate=0.1, val=True, verbose=True, vid_stride=1, visualize=False, warmup_bias_lr=0.1, warmup_epochs=3.0, warmup_momentum=0.8, weight_decay=0.0005, workers=8, workspace=None\n", "Overriding model.yaml nc=80 with nc=1\n", "\n", "                   from  n    params  module                                       arguments                     \n", "  0                  -1  1       928  ultralytics.nn.modules.conv.Conv             [3, 32, 3, 2]                 \n", "  1                  -1  1     18560  ultralytics.nn.modules.conv.Conv             [32, 64, 3, 2]                \n", "  2                  -1  1     29056  ultralytics.nn.modules.block.C2f             [64, 64, 1, True]             \n", "  3                  -1  1     73984  ultralytics.nn.modules.conv.Conv             [64, 128, 3, 2]               \n", "  4                  -1  2    197632  ultralytics.nn.modules.block.C2f             [128, 128, 2, True]           \n", "  5                  -1  1    295424  ultralytics.nn.modules.conv.Conv             [128, 256, 3, 2]              \n", "  6                  -1  2    788480  ultralytics.nn.modules.block.C2f             [256, 256, 2, True]           \n", "  7                  -1  1   1180672  ultralytics.nn.modules.conv.Conv             [256, 512, 3, 2]              \n", "  8                  -1  1   1838080  ultralytics.nn.modules.block.C2f             [512, 512, 1, True]           \n", "  9                  -1  1    656896  ultralytics.nn.modules.block.SPPF            [512, 512, 5]                 \n", " 10                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']          \n", " 11             [-1, 6]  1         0  ultralytics.nn.modules.conv.Concat           [1]                           \n", " 12                  -1  1    591360  ultralytics.nn.modules.block.C2f             [768, 256, 1]                 \n", " 13                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']          \n", " 14             [-1, 4]  1         0  ultralytics.nn.modules.conv.Concat           [1]                           \n", " 15                  -1  1    148224  ultralytics.nn.modules.block.C2f             [384, 128, 1]                 \n", " 16                  -1  1    147712  ultralytics.nn.modules.conv.Conv             [128, 128, 3, 2]              \n", " 17            [-1, 12]  1         0  ultralytics.nn.modules.conv.Concat           [1]                           \n", " 18                  -1  1    493056  ultralytics.nn.modules.block.C2f             [384, 256, 1]                 \n", " 19                  -1  1    590336  ultralytics.nn.modules.conv.Conv             [256, 256, 3, 2]              \n", " 20             [-1, 9]  1         0  ultralytics.nn.modules.conv.Concat           [1]                           \n", " 21                  -1  1   1969152  ultralytics.nn.modules.block.C2f             [768, 512, 1]                 \n", " 22        [15, 18, 21]  1   2116435  ultralytics.nn.modules.head.Detect           [1, [128, 256, 512]]          \n", "Model summary: 129 layers, 11,135,987 parameters, 11,135,971 gradients, 28.6 GFLOPs\n", "\n", "Transferred 349/355 items from pretrained weights\n", "Freezing layer 'model.22.dfl.conv.weight'\n", "\u001b[34m\u001b[1mtrain: \u001b[0mFast image access  (ping: 0.10.0 ms, read: 225.957.4 MB/s, size: 32.4 KB)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[34m\u001b[1mtrain: \u001b[0mScanning D:\\DoctorResearch\\work\\国赛\\题目\\task\\t2\\datasets\\medicine\\Images\\train... 0 images, 1276 backgrounds, 0 corrupt: 100%|██████████| 1276/1276 [00:00<00:00, 4054.37it/s]\u001b[0m"]}, {"name": "stdout", "output_type": "stream", "text": ["WARNING \u001b[34m\u001b[1mtrain: \u001b[0mNo labels found in D:\\DoctorResearch\\work\\\\\\task\\t2\\datasets\\medicine\\Images\\train.cache. See https://docs.ultralytics.com/datasets for dataset formatting guidance.\n", "\u001b[34m\u001b[1mtrain: \u001b[0mNew cache created: D:\\DoctorResearch\\work\\\\\\task\\t2\\datasets\\medicine\\Images\\train.cache\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["WARNING Labels are missing or empty in D:\\DoctorResearch\\work\\\\\\task\\t2\\datasets\\medicine\\Images\\train.cache, training may not work correctly. See https://docs.ultralytics.com/datasets for dataset formatting guidance.\n", "\u001b[34m\u001b[1mval: \u001b[0mFast image access  (ping: 0.10.0 ms, read: 268.895.3 MB/s, size: 29.9 KB)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[34m\u001b[1mval: \u001b[0mScanning D:\\DoctorResearch\\work\\国赛\\题目\\task\\t2\\datasets\\medicine\\Images\\valid.cache... 0 images, 365 backgrounds, 0 corrupt: 100%|██████████| 365/365 [00:00<?, ?it/s]\u001b[0m"]}, {"name": "stdout", "output_type": "stream", "text": ["WARNING Labels are missing or empty in D:\\DoctorResearch\\work\\\\\\task\\t2\\datasets\\medicine\\Images\\valid.cache, training may not work correctly. See https://docs.ultralytics.com/datasets for dataset formatting guidance.\n", "Plotting labels to runs\\detect\\train2\\labels.jpg... \n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["WARNING zero-size array to reduction operation maximum which has no identity\n", "\u001b[34m\u001b[1moptimizer:\u001b[0m 'optimizer=auto' found, ignoring 'lr0=0.01' and 'momentum=0.937' and determining best 'optimizer', 'lr0' and 'momentum' automatically... \n", "\u001b[34m\u001b[1moptimizer:\u001b[0m AdamW(lr=0.002, momentum=0.9) with parameter groups 57 weight(decay=0.0), 64 weight(decay=0.0005), 63 bias(decay=0.0)\n", "Image sizes 640 train, 640 val\n", "Using 0 dataloader workers\n", "Logging results to \u001b[1mruns\\detect\\train2\u001b[0m\n", "Starting training for 100 epochs...\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      1/100         0G          0      81.69          0          0        640: 100%|██████████| 80/80 [06:32<00:00,  4.91s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.70s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      2/100         0G          0     0.4529          0          0        640: 100%|██████████| 80/80 [06:32<00:00,  4.90s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.65s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      3/100         0G          0  0.0002366          0          0        640: 100%|██████████| 80/80 [06:34<00:00,  4.93s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.65s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      4/100         0G          0  1.073e-07          0          0        640: 100%|██████████| 80/80 [06:33<00:00,  4.92s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      5/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:40<00:00,  5.00s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.70s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      6/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:42<00:00,  5.03s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.73s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      7/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:41<00:00,  5.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.70s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      8/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:41<00:00,  5.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.70s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      9/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:42<00:00,  5.03s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.72s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     10/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:43<00:00,  5.04s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.70s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     11/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:32<00:00,  4.90s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.65s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     12/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:30<00:00,  4.89s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.65s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     13/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:31<00:00,  4.89s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     14/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:30<00:00,  4.88s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     15/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:29<00:00,  4.87s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     16/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:29<00:00,  4.86s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     17/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:29<00:00,  4.87s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     18/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:30<00:00,  4.88s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     19/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:29<00:00,  4.87s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.65s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     20/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:31<00:00,  4.89s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     21/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:29<00:00,  4.87s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     22/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:30<00:00,  4.88s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.66s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     23/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:30<00:00,  4.88s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.66s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     24/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:29<00:00,  4.87s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.65s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     25/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:30<00:00,  4.88s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     26/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:29<00:00,  4.87s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.65s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     27/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:38<00:00,  4.99s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.71s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     28/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:40<00:00,  5.01s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.71s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     29/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:40<00:00,  5.01s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.68s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     30/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:41<00:00,  5.01s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.71s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     31/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:42<00:00,  5.03s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.70s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     32/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:42<00:00,  5.03s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.68s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     33/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:42<00:00,  5.03s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.69s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     34/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:39<00:00,  5.00s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.71s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     35/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:35<00:00,  4.94s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     36/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:30<00:00,  4.89s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.65s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     37/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:29<00:00,  4.87s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.63s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     38/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:30<00:00,  4.88s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     39/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:30<00:00,  4.89s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     40/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:29<00:00,  4.87s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     41/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:29<00:00,  4.86s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     42/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:29<00:00,  4.87s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.63s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     43/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:29<00:00,  4.87s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     44/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:30<00:00,  4.88s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     45/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:30<00:00,  4.88s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.65s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     46/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:29<00:00,  4.87s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     47/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:29<00:00,  4.86s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     48/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:31<00:00,  4.89s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     49/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:31<00:00,  4.89s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.69s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     50/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:38<00:00,  4.98s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.69s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     51/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:38<00:00,  4.99s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.70s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     52/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:39<00:00,  5.00s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.73s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     53/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:39<00:00,  4.99s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.71s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     54/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:40<00:00,  5.00s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.69s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     55/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:41<00:00,  5.01s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.71s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     56/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:41<00:00,  5.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.71s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     57/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:40<00:00,  5.00s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.70s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     58/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:39<00:00,  4.99s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.69s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     59/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:41<00:00,  5.02s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     60/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:29<00:00,  4.87s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     61/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:31<00:00,  4.89s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.65s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     62/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:29<00:00,  4.87s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.65s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     63/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:30<00:00,  4.88s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.66s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     64/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:29<00:00,  4.87s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.66s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     65/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:30<00:00,  4.88s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.65s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     66/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:30<00:00,  4.88s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     67/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:29<00:00,  4.87s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     68/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:29<00:00,  4.87s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.63s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     69/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:30<00:00,  4.88s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.63s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     70/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:29<00:00,  4.87s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.63s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     71/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:28<00:00,  4.86s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.65s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     72/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:29<00:00,  4.87s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     73/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:28<00:00,  4.86s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.63s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     74/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:28<00:00,  4.86s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.65s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     75/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:29<00:00,  4.87s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     76/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:40<00:00,  5.00s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.70s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     77/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:39<00:00,  4.99s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.68s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     78/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:39<00:00,  5.00s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.71s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     79/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:39<00:00,  5.00s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.71s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     80/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:40<00:00,  5.01s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.71s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     81/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:39<00:00,  4.99s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.70s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     82/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:39<00:00,  4.99s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.71s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     83/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:40<00:00,  5.01s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.69s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     84/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:32<00:00,  4.90s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.65s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     85/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:31<00:00,  4.89s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     86/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:30<00:00,  4.88s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.65s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     87/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:30<00:00,  4.88s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.63s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     88/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:30<00:00,  4.89s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     89/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:29<00:00,  4.87s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     90/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:31<00:00,  4.90s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Closing dataloader mosaic\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     91/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:28<00:00,  4.86s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     92/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:27<00:00,  4.85s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.65s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     93/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:28<00:00,  4.85s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     94/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:29<00:00,  4.86s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     95/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:27<00:00,  4.84s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     96/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:28<00:00,  4.85s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:43<00:00,  3.65s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     97/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:27<00:00,  4.84s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.68s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     98/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:36<00:00,  4.96s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.70s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     99/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:36<00:00,  4.96s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.71s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["    100/100         0G          0          0          0          0        640: 100%|██████████| 80/80 [06:36<00:00,  4.96s/it]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:44<00:00,  3.69s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "100 epochs completed in 12.166 hours.\n", "Optimizer stripped from runs\\detect\\train2\\weights\\last.pt, 22.5MB\n", "Optimizer stripped from runs\\detect\\train2\\weights\\best.pt, 22.5MB\n", "\n", "Validating runs\\detect\\train2\\weights\\best.pt...\n", "Ultralytics 8.3.170  Python-3.8.20 torch-2.4.1+cpu CPU (AMD Ryzen 5 7500F 6-Core Processor)\n", "Model summary (fused): 72 layers, 11,125,971 parameters, 0 gradients, 28.4 GFLOPs\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:39<00:00,  3.30s/it]\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:668: RuntimeWarning: Mean of empty slice.\n", "  ax.plot(px, py.mean(1), linewidth=3, color=\"blue\", label=f\"all classes {ap[:, 0].mean():.3f} mAP@0.5\")\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:192: RuntimeWarning: invalid value encountered in scalar divide\n", "  ret = ret.dtype.type(ret / rcount)\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:713: RuntimeWarning: Mean of empty slice.\n", "  y = smooth(py.mean(0), 0.1)\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:713: RuntimeWarning: Mean of empty slice.\n", "  y = smooth(py.mean(0), 0.1)\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:713: RuntimeWarning: Mean of empty slice.\n", "  y = smooth(py.mean(0), 0.1)\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\numpy\\core\\_methods.py:184: RuntimeWarning: invalid value encountered in divide\n", "  ret = um.true_divide(\n", "D:\\Program Files\\Anaconda3\\envs\\yolov8\\lib\\site-packages\\ultralytics\\utils\\metrics.py:850: RuntimeWarning: Mean of empty slice.\n", "  i = smooth(f1_curve.mean(0), 0.1).argmax()  # max F1 index\n"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365          0          0          0          0          0\n", "WARNING no labels found in detect set, can not compute metrics without labels\n", "Speed: 1.1ms preprocess, 103.9ms inference, 0.0ms loss, 0.1ms postprocess per image\n", "Results saved to \u001b[1mruns\\detect\\train2\u001b[0m\n", "\n", "0: 640x640 (no detections), 96.7ms\n", "Speed: 2.6ms preprocess, 96.7ms inference, 1.6ms postprocess per image at shape (1, 3, 640, 640)\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[12], line 2\u001b[0m\n\u001b[0;32m      1\u001b[0m train()\n\u001b[1;32m----> 2\u001b[0m \u001b[43mtest_img\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m      3\u001b[0m predict()\n", "Cell \u001b[1;32mIn[10], line 10\u001b[0m, in \u001b[0;36mtest_img\u001b[1;34m()\u001b[0m\n\u001b[0;32m      8\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[0;32m      9\u001b[0m     cv2\u001b[38;5;241m.\u001b[39mimshow(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124myolo\u001b[39m\u001b[38;5;124m\"\u001b[39m, ann)\n\u001b[1;32m---> 10\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[43mcv2\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mwait<PERSON>ey\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m)\u001b[49m \u001b[38;5;241m&\u001b[39m \u001b[38;5;241m0xFF\u001b[39m \u001b[38;5;241m==\u001b[39m \u001b[38;5;28mord\u001b[39m(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mq\u001b[39m\u001b[38;5;124m'\u001b[39m):\n\u001b[0;32m     11\u001b[0m         \u001b[38;5;28;01<PERSON>ak\u001b[39;00m\n\u001b[0;32m     12\u001b[0m \u001b[38;5;66;03m# 设置保存图片的路径\u001b[39;00m\n", "\u001b[1;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["train()\n", "test_img()\n", "predict()\n", "# onnx()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["['tub03008', 'tub02009', 'tub01005']"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "\n", "def display_number(num, total_digits=8):\n", "    \"\"\"\n", "    在8位数码管上右对齐显示一个浮点数，支持小数点。\n", "    参数:\n", "        num: float 或 np.float32/np.float64 类型的数字\n", "        total_digits: 数码管总位数（默认8）\n", "    \"\"\"\n", "    if not isinstance(num, (float, np.floating)):\n", "        raise ValueError(\"输入必须是浮点数类型（float 或 np.float）\")\n", "\n", "    # 步骤1: 转为标准 float 并格式化为最多8位有效数字的字符串\n", "    val = float(num)\n", "\n", "    # 处理极小或极大数（科学计数法），但我们假设是普通小数\n", "    # 使用 %g 自动选择最短表示，最多8位有效数字\n", "    s = f\"{val:.7g}\"  # 最多8位有效数字\n", "\n", "    # 检查长度（去除小数点后）不能超过 total_digits\n", "    digits_only = s.replace('.', '')\n", "    if len(digits_only) > total_digits:\n", "        # 截断到最多 total_digits 位有效数字\n", "        s = f\"{val:.{total_digits-1}g}\"  # 重新格式化\n", "        digits_only = s.replace('.', '')\n", "        if len(digits_only) > total_digits:\n", "            # 再截一次（应对科学计数法）\n", "            s = s[:total_digits + (1 if '.' in s else 0)]  # 粗略截断\n", "\n", "    # 步骤2: 解析字符串，生成带小数点标记的数值列表\n", "    values = []\n", "    i = 0\n", "    while i < len(s):\n", "        ch = s[i]\n", "        if ch == '.':\n", "            if not values:\n", "                raise ValueError(\"格式错误：小数点不能在开头\")\n", "            values[-1] += 128  # 给前一个数字加小数点\n", "        elif ch.isdigit():\n", "            digit = int(ch)\n", "            values.append(digit)\n", "        else:\n", "            raise ValueError(f\"非法字符: {ch}\")\n", "        i += 1\n", "\n", "    # 步骤3: 右对齐 → 左边补 0\n", "    if len(values) > total_digits:\n", "        raise ValueError(f\"数字太多（{len(values)}位），超过数码管容量（{total_digits}位）\")\n", "\n", "    command_list = []\n", "\n", "    # 步骤4: 发送信号（从左到右，对应从右到左编号 008 ~ 001）\n", "    for idx, value in enumerate(values):\n", "        pos_from_right = len(values) - idx  # 右对齐\n", "        pos_str = f\"{pos_from_right:02d}\"\n", "        command = f\"tub{pos_str}{value:03d}\"\n", "        command_list.append(command)\n", "    return command_list\n", "\n", "\n", "display_number(0.895)[1:]"]}], "metadata": {"kernelspec": {"display_name": "instdiff", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 4}