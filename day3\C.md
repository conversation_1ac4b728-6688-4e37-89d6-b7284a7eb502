中华人民共和国第三届职业技能大赛
人工智能工程技术任务书

模块 C、综合工程技术应用
一、任务要求 
根据项目要求完成人工智能综合工程技术应用代码开发，将任务过程中填写的代码截图和每个步骤的输出结果粘贴在“竞赛任务应答书.docxx”的指定位置中（如某一步骤无输出，应填“无”）。最后按照《选手指引》要求将“竞赛任务应答书.docxx”改名为“抽签号+天数”（如“01_day3.docx”），并导出整个task文件夹的压缩包，改名为“抽签号+天数”（如“01_day3.zip”），保存至指定位置。  
二、任务环境 
硬件资源:高性能GPU计算机、边缘设备场景验证平台、人工智能模型训练系统 
软件资源:“竞赛资源.zip”、pytorch深度学习框架、模型量化软件、模型推理验证软件
三、任务说明 
本次任务中，在ipynb文件中运行各单元格时，选择内核时应该选择python环境module_C_1_env。
模块C的实验需要用到两个环境，因此，使用python命令运行各py文件时，需要在对应文件所在目录打开终端，并使用正确环境（环境有module_C_1_env和module_C_2_env两个，会在对应步骤进行具体说明）
在竞赛系统外，主机的环境下运行py文件时，不需要切换环境，但需要加上sudo命令（密码为hg）。
1、药品知识问答 
在现实医疗场景中，药品的种类繁多且不断更新，其功效、用法用量、禁忌、不良反应等信息复杂多样且相互关联。为满足医疗从业者、患者及普通民众对药品知识的精准获取需求，同时确保在面对不断涌现的新药及药品相关复杂问题时，问答系统仍能保持高效准确，我们设置一项药品知识问答任务。通过对优秀的通用基座模型进行大量药品图文对的训练，以显著提高通用模型在医疗场景下的泛化能力，适应复杂、开放的药品知识问答需求，对医疗实践、患者自我健康管理以及公众健康知识普及具有重要意义。
同时，随着人工智能技术的飞速发展，边缘计算在各个领域的应用日益广泛。在医疗健康场景中，将训练好的模型部署到边缘设备，实现对药品信息的快速识别与处理，具有极高的实用价值。本任务旨在让考生亲身体验将训练好的模型部署到实际边缘设备的全流程，将理论知识与实践操作紧密结合，提升在真实场景下解决实际问题的能力。考生需将训练好的药品问答模型进行转换、编译，并成功部署到指定的边缘设备中。随后，利用边缘设备的摄像头在真实生活环境中进行拍照，获取药盒正面图片，并通过提供的测试平台调用边缘设备进行问答测试，验证模型在实际应用中的性能和准确性。
进入人工智能模型训练系统，打开“task”文件夹，文件夹中提供了所需的全部任务文件。请打开“task/QA/medicines_qa.ipynb”文件（选择kernel环境应该为module_C_1_env），根据提示在该文件内或其他指定位置编写代码，完成以下操作: 
注：进行任务前，请检查第一个单元格的路径是否无误，如果和自己保存路径有区别，需要自行调整至正确的绝对路径。然后运行该单元格。
（1）步骤1 加载模型制作训练集
部分代码已给出，请根据提示，将代码补充完整。 
①请在<1>处补充resize函数代码，将药品图片resize到合适的大小，需设置最大尺寸为512，将图像转换为RGB模式，并按指定最大尺寸等比缩放图像并使用抗锯齿算法保证缩放后的画质，并保存到合适路径；
②可以看到，“task/QA/source/药品信息”中，不同药物正面图具有相同的命名规则，请在<2>处补充代码，找出药品的正面图，并调用resize函数进行处理。
③请在<3>处补充代码，此处需要调用模型提取药盒的各种信息。构造合适的prompt（需要注意，这里需要加上<image>的特殊标记），正确的messages结构，设定适宜的参数，最终提取模型输出。
④请在<4>处补充代码，构造训练数据集并进行保存。
⑤请根据<5>提示，构造正确的数据集描述写入“source/LLaMA_Factory/data /dataset_info.json”，并将生成的数据放入“source/LLaMA_Factory/data”文件夹中。
将该步骤单元格运行结果保存在“竞赛任务应答书.docx”中指定位置，将该步骤①至⑤处补充的代码截图和数据构造部分都保存在“竞赛任务应答书.docx”中指定位置。第⑤步，将数据文件放入正确的文件夹后，需要截图train_data.json的内容（截一张图即可）以及截图vscode左侧展示的source/LLaMA_Factory/data的文件夹目录（截图的目录结构中需包含train_data.json）保存在“竞赛任务应答书.docx”中对应的输出结果位置。
（2）步骤2 模型训练
①部分代码已给出，请根据提示，将代码补充完整。 
请补充“task/QA/source/LLaMA_Factory/examples/train_lora/qwen2vl_lora_sft.yaml”训练脚本中<1>处的相应参数。
②请根据“task/QA/source/LLaMA_Factory/examples/train_lora/qwen2vl_lora_sft.yaml”文件中的<2>提示命令，开始进行模型训练。
将该步骤①补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。将②的模型训练日志最后部分截图，保存在“竞赛任务应答书.docx”中指定位置。
（3）步骤3 模型导出
①请补充“task/QA/source/LLaMA_Factory/example/merge_lora/qwen2vl_lora_sft.yaml”的<1>中相应参数。
②请根据，在“task/QA/source/LLaMA_Factory/”目录下打开终端，切换环境为module_C_1_env（命令为conda activate module_C_1_env），然后运行“task/QA/source/LLaMA_Factory/example/merge_lora/qwen2vl_lora_sft.yaml”文件给出的<2>提示命令，开始进行模型导出。
将该步骤①补充的代码截图并保存在“竞赛任务应答书.docx”中指定位置。将②的模型导出日志最后部分截图，保存在“竞赛任务应答书.docx”中指定位置。
（4）步骤4 模型能力评测
①请点击竞赛系统的远程桌面，可以看到桌面存在“多模态模型评测工具”，打开该工具，选择模型为“Qwen2_VL”，并将目录选择步骤3中导出的模型所在的保存目录，点击进行模型评测。
将系统模型评测结果进行截图并保存在“竞赛任务应答书.docx”中指定位置，截图需要包含选择模型文件界面及accuracy的输出界面。
（5）步骤5 模型量化与验证（该步骤在竞赛系统外，主机的ubuntu环境中进行操作，且与后续步骤独立）
①请将步骤3中导出的模型文件整个打包，然后下载到竞赛系统外并解压，选择主目录下的autobit_qt文件夹并在文件夹目录下打开终端，运行命令“sudo ./run_autobit.sh”打开模型转换工具，选择正确页面执行模型量化操作，配置文件使用默认的“W4A16”，量化完成后，将量化完成的截图、转换后的模型所在文件夹的截图均保存在“竞赛任务应答书.docx”中指定位置。
②请阅读主目录下的autobit_qt文件夹下的infer_autoawq_cli.py文件，在该目录下打开终端，根据命令，进行一次量化模型的推理验证（注：在终端运行时，需要添加sudo命令），验证使用文件夹中的demo.jpg，问题可自主编辑。将推理验证的输出截图保存在“竞赛任务应答书.docx”中指定位置。
（6）步骤6.模型转换
①请在<1>处补充代码，在这里填写步骤3导出的模型所在的路径。
②由于模型可能需要每批次输入一定数量的图像，N 可能不满足 temporal_patch_size 的要求。所以我们需要在 N == 1 或 N % temporal_patch_size != 0 的情况下通过重复图像来填充批次。请在<2>处补充代码，使用repeat函数调整图像批次的大小，确保其符合temporal_patch_size。
③请在<3>处补充代码，确保在第一步时，调用视觉模块的前向传播方法,相比于第二步，这里还需要传入包含grid_t, grid_h, grid_w的grid_thw参数（该参数参与模型的位置嵌入计算）。需要确保grid_thw的形状为 (1, 3)，即在第一维增加一个批次维度。
④在<4>处，我们需要调用torch.onnx.export来导出ONNX模型。导出时，需要提供正确的输入张量，设置opset_version参数为19。
⑤在<5>处，填写之前第④步得到的onnx模型所在路径，并执行导出为rknn模型。
将该步骤①-⑤补充的代码保存在“竞赛任务应答书.docx”中指定位置。将步骤涉及到的运行输出截图均保存在“竞赛任务应答书.docx”中指定位置。
（7）步骤7.模型编译
请在source目录下打开终端，通过conda activate module_C_2_env命令切换为该步骤的指定环境。
然后，请打开source/make_input_embeds.py文件。
①请在<1>处补充代码，在这里填写步骤3导出的模型所在的路径。
②请补全<2>处的代码，这里需要从data/inputs_embeds/加载各inputs_embeds。补全之后，在终端用python命令运行该文件。
打开source/export_rkllm.py文件。
③请在<3>处补充代码，在这里填写步骤3导出的模型所在的路径。
④请补全<4>处的代码，需要设置量化类型为w8a8。补全之后，在终端用  python命令运行该文件。
将该步骤①-④补充的代码保存在“竞赛任务应答书.docx”中指定位置。将步骤涉及到的运行输出截图均保存在“竞赛任务应答书.docx”中指定位置。
（8）步骤8.板端测试
请按《人工智能工程技术设备使用说明》将rknn模型和rkllm模型传入模型部署及边缘计算平台的userdata目录下，编辑边缘设备/etc/inference-test-app目录下的配置文件inference.conf，仅修改两个模型路径的配置，确保两个模型的路径正确。
	然后，在模型部署及边缘计算平台上的终端中，输入命令：
sudo inference-test-app
打开模型推理验证软件，在平台要求进行拍照和测试。
模型推理验证软件一共有五个题目，按先后顺序，请分别按以下内容要求进行拍照，和进行推理验证：
1、摆放一个阿托伐他汀钙片的正面照，进行拍照推理。
2、摆放一个双氯芬酸钠缓释胶囊的正面照，进行拍照推理。
3、摆放一个盐酸左西替利嗪片的正面照，进行拍照推理。
4、摆放一个阿司匹林肠溶片的正面照，进行拍照推理。
5、面朝模型部署及边缘计算平台，左右摆放一个醋酸泼尼松片（左）和阿托伐他汀钙片（右）的正面照，
将5个测试结果截图并保存在“竞赛任务应答书.docx”中指定位置。

注：“竞赛任务应答书.docxx”最后提交时应该改名为“抽签号+天数”（如“01_day3.docx”）

