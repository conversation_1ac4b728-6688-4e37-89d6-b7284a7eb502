{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 1.药品知识回答"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 步骤1.加载模型制作训练集"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["`Qwen2VLRotaryEmbedding` can now be fully parameterized by passing the model config through the `config` argument. All other arguments will be removed in v4.46\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "02acd45460074c23a5552f51239ba17e", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import os\n", "from PIL import Image\n", "import json\n", "from transformers import Qwen2VLForConditionalGeneration, AutoTokenizer, AutoProcessor\n", "from qwen_vl_utils import process_vision_info\n", "import torch\n", "\n", "# 需要写绝对路径，以防之后路径出错\n", "base_dir = '/home/<USER>/userSpace/studnet/home/<USER>/module_C/模块C/resource/药品信息'\n", "resized_dir = '/home/<USER>/userSpace/studnet/home/<USER>/module_C/模块C/resource/药品信息/resized_512'\n", "model_dir = \"/home/<USER>/userSpace/studnet/Qwen2-VL-2B-Instruct\"\n", "os.makedirs(resized_dir, exist_ok=True)\n", "\n", "model = Qwen2VLForConditionalGeneration.from_pretrained(model_dir, torch_dtype=\"auto\", device_map=\"auto\")\n", "processor = AutoProcessor.from_pretrained(model_dir)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <1> resize函数：将图片 resize 到合适大小\n", "def resize_image(input_path, output_path, max_size=512):\n", "    try:\n", "        img = Image.open(input_path)\n", "        img = img.convert(\"RGB\")\n", "        img.thumbnail((max_size, max_size), Image.ANTIALIAS)\n", "        os.makedirs(os.path.dirname(output_path), exist_ok=True)\n", "        img.save(output_path, format='JPEG')\n", "        return True\n", "    except Exception as e:\n", "        print(f\"Failed to process {input_path}: {e}\")\n", "        return False"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_12232/3658047278.py:6: DeprecationWarning: ANTIALIAS is deprecated and will be removed in Pillow 10 (2023-07-01). Use LANCZOS or Resampling.LANCZOS instead.\n", "  img.thumbnail((max_size, max_size), Image.ANTIALIAS)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["共处理 20 个子文件夹，结果保存到 /home/<USER>/userSpace/studnet/home/<USER>/module_C/模块C/resource/药品信息/train_data.json\n"]}], "source": ["train_data = []\n", "\n", "# <2> 读取子文件夹图片resize并找出药品正面图 两处代码共1分\n", "\n", "for sub in os.listdir(base_dir):\n", "    sub_path = os.path.join(base_dir, sub)\n", "    if not os.path.isdir(sub_path):\n", "        continue\n", "\n", "    imgs = [fname for fname in sorted(os.listdir(sub_path))\n", "            if fname.lower().endswith(('.jpeg'))]\n", "    if not imgs:\n", "        continue\n", "\n", "    front_candidates = ['1.jpeg']\n", "    front_name = None\n", "    for name in imgs:\n", "        if name in front_candidates:\n", "            front_name = name\n", "            break\n", "    if front_name is None:\n", "        front_name = imgs[0]\n", "\n", "    \n", "    resized_paths_all = []\n", "    resized_front_path = None\n", "\n", "    for name in imgs:\n", "        orig_path = os.path.join(sub_path, name)\n", "        \n", "        out_sub_dir = os.path.join(resized_dir, sub)\n", "        os.makedirs(out_sub_dir, exist_ok=True)\n", "        out_path = os.path.join(out_sub_dir, name)\n", "        success = resize_image(orig_path, out_path, max_size=512)\n", "        if success:\n", "            # uri = f\"file://{out_path}\"\n", "            uri = out_path\n", "            resized_paths_all.append(uri)\n", "            if name == front_name:\n", "                resized_front_path = uri\n", "\n", "    \n", "    if resized_front_path is None:\n", "        print(f\"子文件夹 {sub} 未能找到并 resize 出正面图片，跳过\")\n", "        continue\n", "\n", "# <3> 构造 messages，模型多图推理 4处代码共2分\n", "    user_instruction = \"<image>请提取此药品的关键信息：名称、成分、用法用量、适应症、不良反应等。\"\n", "    content = []\n", "    for p in resized_paths_all:\n", "        content.append({\"type\": \"image\", \"image\": p})\n", "    content.append({\"type\": \"text\", \"text\": user_instruction})\n", "    messages = [\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": content\n", "        }\n", "    ]\n", "    \n", "    text = processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)\n", "    image_inputs, video_inputs = process_vision_info(messages)\n", "    inputs = processor(\n", "        text=[text],\n", "        images=image_inputs,\n", "        videos=video_inputs,\n", "        padding=True,\n", "        return_tensors=\"pt\"\n", "    )\n", "    inputs = inputs.to(model.device)\n", "    \n", "    with torch.no_grad():\n", "        generated_ids = model.generate(**inputs, max_new_tokens=256, repetition_penalty=1.2, do_sample=True, temperature=0.7, top_p=0.9)\n", "    \n", "    generated_ids_trimmed = [\n", "        out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)\n", "    ]\n", "    output_texts = processor.batch_decode(\n", "        generated_ids_trimmed,\n", "        skip_special_tokens=True,\n", "        clean_up_tokenization_spaces=False\n", "    )\n", "    answer = output_texts[0].strip()\n", "\n", "# <4> 构造并保存训练集，3处代码共1.5分\n", "    entry = {\n", "        \"conversations\": [\n", "            {\"from\": \"human\", \"value\": user_instruction},\n", "            {\"from\": \"gpt\", \"value\": answer}\n", "        ],\n", "        \"images\": [resized_front_path]\n", "    }\n", "    train_data.append(entry)\n", "\n", "\n", "output_json_path = os.path.join(base_dir, 'train_data.json')\n", "with open(output_json_path, 'w', encoding='utf-8') as f:\n", "    json.dump(train_data, f, ensure_ascii=False, indent=2)\n", "\n", "print(f\"共处理 {len(train_data)} 个子文件夹，结果保存到 {output_json_path}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <5>  在 LLaMA-Factory/data/dataset_info.json 中仿照已有数据格式补充填写新生成的json数据，命名为drug_info，并将新生成的 train.json 放入 LLaMA-Factory/data 中。共3分。\n", "\"drug_info\": {\n", "    \"file_name\": \"train_data.json\",\n", "    \"formatting\": \"sharegpt\",\n", "    \"columns\": {\n", "        \"messages\": \"conversations\",\n", "        \"images\": \"images\"\n", "    },\n", "    \"tags\": {\n", "        \"role_tag\": \"from\",\n", "        \"content_tag\": \"value\",\n", "        \"user_tag\": \"human\",\n", "        \"assistant_tag\": \"gpt\"\n", "    }\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 步骤2. 模型训练"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <1>  填充examples/train_lora/qwen2_vl_lora_sft.yaml中相应参数，7处代码各0.5分，共3.5分。\n", "\n", "### model\n", "model_name_or_path: /home/<USER>/userSpace/studnet/Qwen2-VL-2B-Instruct\n", "\n", "### method\n", "stage: sft\n", "do_train: true\n", "finetuning_type: lora\n", "lora_rank: 8\n", "lora_target: all\n", "\n", "### dataset\n", "dataset: drug_info, mllm_demo, identity, alpaca_en_demo\n", "template: qwen2_vl\n", "cutoff_len: 2048\n", "max_samples: 1000\n", "overwrite_cache: true\n", "preprocessing_num_workers: 16\n", "dataloader_num_workers: 4\n", "\n", "### output\n", "output_dir: saves/qwen2_vl-2b/lora/sft\n", "logging_steps: 10\n", "save_steps: 500\n", "plot_loss: true\n", "overwrite_output_dir: true\n", "save_only_model: false\n", "report_to: none  \n", "\n", "### train\n", "per_device_train_batch_size: 1\n", "gradient_accumulation_steps: 8\n", "learning_rate: 1.0e-4\n", "num_train_epochs: 3.0\n", "lr_scheduler_type: cosine\n", "warmup_ratio: 0.1\n", "bf16: true\n", "ddp_timeout: 180000000\n", "resume_from_checkpoint: null\n", "\n", "### eval\n", "val_size: 0.1\n", "per_device_eval_batch_size: 1\n", "eval_strategy: steps\n", "eval_steps: 500"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <2> 开始训练 llamafactory-cli train examples/train_lora/qwen2vl_lora_sft.yaml，0.5分"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 步骤3.模型导出"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <1> 确认 examples/merge_lora/llama3_lora_sft.yaml 中相应参数，进行模型导出。0.5分\n", "\n", "### model\n", "model_name_or_path: /home/<USER>/userSpace/studnet/Qwen2-VL-2B-Instruct\n", "adapter_name_or_path: saves/qwen2_vl-2b/lora/sft\n", "template: qwen2_vl\n", "trust_remote_code: true\n", "\n", "### export\n", "export_dir: ans/qwen2vl_lora_sft\n", "export_size: 5\n", "export_device: auto  \n", "export_legacy_format: false"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <2> 开始导出 llamafactory-cli export examples/merge_lora/qwen2vl_lora_sft.yaml，0.5分"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 步骤4.模型能力评测"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <1> 根据 Accuracy 及 规范输出数量 进行打分\n", "# 相同分数情况下可比较规范输出数量"]}, {"cell_type": "markdown", "metadata": {}, "source": ["| Accuracy 范围         | 得分  |\n", "|----------------------|-------|\n", "| ≤ 0.60               | 0     |\n", "| 0.60 < Accuracy ≤ 0.65 | 1     |\n", "| 0.65 < Accuracy ≤ 0.70 | 1.5   |\n", "| 0.70 < Accuracy ≤ 0.75 | 2     |\n", "| 0.75 < Accuracy ≤ 0.80 | 2.5   |\n", "| 0.80 < Accuracy ≤ 0.85 | 3     |\n", "| > 0.85               | 3.5   |"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 2. 场景实践"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 步骤1.模型转换、编译、部署、推理"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <10> 参考 \"~/模块C/resource/rknn-llm/tree/main/examples/Qwen2-VL_Demo\"文档进行模型转换和部署推理 共3分\n", "# 模型转换（rknn、rkllm转换成功 1分）\n", "# 模型编译 1分\n", "# 模型部署、推理 1分"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["The argument `trust_remote_code` is to be used with Auto classes. It has no effect here and is ignored.\n", "`Qwen2VLRotaryEmbedding` can now be fully parameterized by passing the model config through the `config` argument. All other arguments will be removed in v4.46\n"]}], "source": ["import numpy as np\n", "import os\n", "import torch\n", "from transformers import Qwen2VLForConditionalGeneration, AutoTokenizer\n", "import torch.nn.functional as F\n", "\n", "# 加载本地模型\n", "path = '/home/<USER>/userSpace/studnet/home/<USER>/module_C/模块C/resource/LLaMA-Factory-0.9.1/ans/qwen2vl_lora_sft'\n", "batch = 1\n", "height = 392\n", "width = 392\n", "savepath = 'qwen2-vl-2b/qwen2_vl_2b_vision.onnx'\n", "\n", "model = Qwen2VLForConditionalGeneration.from_pretrained(\n", "    path,\n", "    torch_dtype=torch.float32, # 注意此处的数据类型，由于 rknn 目前仅支持 float32 ，因此需要指定；若是在加载权重时限制了数据类型，需要自行修改config.json中的 \"use_flash_attn\" 参数为 false\n", "    low_cpu_mem_usage=True,\n", "    trust_remote_code=True).eval()\n", "tokenizer = AutoTokenizer.from_pretrained(path, trust_remote_code=True, use_fast=False)\n", "\n", "N = batch                           # batch size\n", "channel = 3                                 # 3 for RGB\n", "H = height                         # image height, must be divisible by (merge_size * patch_size)\n", "W = width                          # image width, must be divisible by (merge_size * patch_size)\n", "merge_size = 2\n", "temporal_patch_size = 2\n", "patch_size = 14\n", "grid_t = N // temporal_patch_size if N%temporal_patch_size == 0 else N // temporal_patch_size + 1\n", "grid_h = H // patch_size\n", "grid_w = W // patch_size\n", "\n", "def export_onnx(image, step):\n", "    if N == 1:\n", "        images = image.repeat(temporal_patch_size, 1, 1, 1)\n", "    elif N % temporal_patch_size != 0:\n", "        repeat_time = temporal_patch_size - N % temporal_patch_size\n", "        repeat_image = image[-1:, ...].repeat(repeat_time, 1, 1, 1)\n", "        images = torch.cat((image, repeat_image), dim=0)\n", "    patches = images.reshape(grid_t, temporal_patch_size, channel, grid_h//merge_size, merge_size, patch_size, grid_w//merge_size, merge_size, patch_size)\n", "    patches = patches.permute(0, 3, 6, 4, 7, 2, 1, 5, 8)\n", "    flatten_patches = patches.reshape(grid_t * grid_h * grid_w, channel * temporal_patch_size * patch_size * patch_size)\n", "    model.visual.forward = forward_new(model.visual)\n", "    if step == 1:\n", "        feature = model.visual(flatten_patches, torch.tensor([grid_t, grid_h, grid_w]).unsqueeze(0))\n", "    else:\n", "        feature = model.visual(flatten_patches)\n", "    return feature\n", "\n", "def forward_new(self):\n", "    def tmp (hidden_states, grid_thw=None):\n", "        hidden_states = self.patch_embed(hidden_states)\n", "        if grid_thw is not None:\n", "            rotary_pos_emb = self.rot_pos_emb(grid_thw)\n", "            cu_seqlens = torch.repeat_interleave(grid_thw[:, 1] * grid_thw[:, 2], grid_thw[:, 0]).cumsum(\n", "                dim=0, dtype=torch.int32\n", "            )\n", "            cu_seqlens = F.pad(cu_seqlens, (1, 0), value=0)\n", "            np.save(\"./rotary_pos_emb.npy\", rotary_pos_emb.cpu().detach().numpy())\n", "            np.save(\"./cu_seqlens.npy\", cu_seqlens.cpu().detach().numpy())\n", "        else:\n", "            rotary_pos_emb = torch.from_numpy(np.load(\"./rotary_pos_emb.npy\")).to(dtype=hidden_states.dtype, device=hidden_states.device)\n", "            cu_seqlens = torch.from_numpy(np.load(\"./cu_seqlens.npy\")).to(dtype=torch.int32, device=hidden_states.device)\n", "        \n", "        for blk in self.blocks:\n", "            hidden_states = blk(hidden_states, cu_seqlens=cu_seqlens, rotary_pos_emb=rotary_pos_emb)\n", "\n", "        return self.merger(hidden_states)\n", "    return tmp\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================step1========================\n", "Generating the rotary_pos_emb and cu_seqlens done.\n"]}], "source": ["# 导出 Vison 部分所对应的 onnx 模型，假设输入是2x3x392x392->(28x28)x(3x2x14x14)\n", "# pixel_values = torch.randn(784, 1176, device=\"cuda\", dtype=torch.float32)\n", "pixel_values = torch.randn(N, channel, H, W, device=\"cpu\", dtype=torch.float32)\n", "model.forward = export_onnx\n", "model = model.to(torch.float32).eval()\n", "\n", "print(\"========================step1========================\")\n", "print(\"Generating the rotary_pos_emb and cu_seqlens done.\")\n", "feature = model(pixel_values, 1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================step2========================\n", "Exporting the vision part of /home/<USER>/userSpace/studnet/home/<USER>/module_C/模块C/resource/LLaMA-Factory-0.9.1/ans/qwen2vl_lora_sft to onnx format.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_4203/1906914566.py:72: TracerWarning: torch.from_numpy results are registered as constants in the trace. You can safely ignore this warning if you use this function to create tensors out of constant variables that would be the same every time you call this function. In any other case, this might cause the trace to be incorrect.\n", "  rotary_pos_emb = torch.from_numpy(np.load(\"./rotary_pos_emb.npy\")).to(dtype=hidden_states.dtype, device=hidden_states.device)\n", "/tmp/ipykernel_4203/1906914566.py:73: Tracer<PERSON><PERSON>ning: torch.from_numpy results are registered as constants in the trace. You can safely ignore this warning if you use this function to create tensors out of constant variables that would be the same every time you call this function. In any other case, this might cause the trace to be incorrect.\n", "  cu_seqlens = torch.from_numpy(np.load(\"./cu_seqlens.npy\")).to(dtype=torch.int32, device=hidden_states.device)\n", "/home/<USER>/miniconda/envs/module_C_1_env/lib/python3.10/site-packages/transformers/models/qwen2_vl/modeling_qwen2_vl.py:399: <PERSON><PERSON><PERSON>arning: Using len to get tensor shape might cause the trace to be incorrect. Recommended usage would be tensor.shape[0]. Passing a tensor of different shape might lead to errors or silently give incorrect results.\n", "  for i in range(1, len(cu_seqlens)):\n"]}], "source": ["# 导出 Vison 部分所对应的 onnx 模型，假设输入是2x3x392x392->(28x28)x(3x2x14x14)\n", "# pixel_values = torch.randn(784, 1176, device=\"cuda\", dtype=torch.float32)\n", "pixel_values = torch.randn(N, channel, H, W, device=\"cpu\", dtype=torch.float32)\n", "model.forward = export_onnx\n", "model = model.to(torch.float32).eval()\n", "\n", "print(\"========================step2========================\")\n", "print(f\"Exporting the vision part of {path} to onnx format.\")\n", "os.makedirs(os.path.dirname(savepath), exist_ok=True)\n", "torch.onnx.export(model, (pixel_values, 2), savepath, opset_version=19)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["I rknn-toolkit2 version: 2.3.2\n", "I Loading : 100%|████████████████████████████████████████████████| 551/551 [00:01<00:00, 385.90it/s]\n", "I OpFusing 1 :  56%|██████████████████████████▎                    | 56/100 [00:00<00:00, 69.02it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"name": "stderr", "output_type": "stream", "text": ["I OpFusing 0 :  99%|██████████████████████████████████████████████▌| 99/100 [00:02<00:00, 34.42it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"name": "stderr", "output_type": "stream", "text": ["I OpFusing 1 :   0%|                                                        | 0/100 [00:10<?, ?it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"name": "stderr", "output_type": "stream", "text": ["I OpFusing 2 :  99%|██████████████████████████████████████████████▌| 99/100 [00:10<00:00,  9.55it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"name": "stderr", "output_type": "stream", "text": ["I OpFusing 0 :  13%|██████                                         | 13/100 [00:10<01:11,  1.21it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"name": "stderr", "output_type": "stream", "text": ["I OpFusing 1 :   0%|                                                        | 0/100 [00:18<?, ?it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"name": "stderr", "output_type": "stream", "text": ["I OpFusing 2 : 100%|██████████████████████████████████████████████| 100/100 [00:18<00:00,  5.32it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"name": "stderr", "output_type": "stream", "text": ["I OpFusing 2 : 100%|██████████████████████████████████████████████| 100/100 [00:32<00:00,  3.08it/s]\n", "I Saving : 100%|██████████████████████████████████████████████████| 327/327 [00:04<00:00, 77.41it/s]\n", "I rknn building ...\n", "E RKNN: [11:45:59.274] REGTASK: The bit width of field value exceeds the limit, target: v2, offset: 0x5048, shift = 19, limit: 0x1fff, value: 0x2b18\n", "E RKNN: [11:45:59.274] REGTASK: The bit width of field value exceeds the limit, target: v2, offset: 0x4038, shift = 0, limit: 0x1fff, value: 0x2b18\n", "E RKNN: [11:45:59.274] REGTASK: The bit width of field value exceeds the limit, target: v2, offset: 0x4038, shift = 16, limit: 0x1fff, value: 0x2b18\n", "E RKNN: [11:45:59.274] REGTASK: The bit width of field value exceeds the limit, target: v2, offset: 0x5048, shift = 19, limit: 0x1fff, value: 0x2b18\n", "E RKNN: [11:45:59.274] REGTASK: The bit width of field value exceeds the limit, target: v2, offset: 0x4038, shift = 0, limit: 0x1fff, value: 0x2b18\n", "E RKNN: [11:45:59.274] REGTASK: The bit width of field value exceeds the limit, target: v2, offset: 0x4038, shift = 16, limit: 0x1fff, value: 0x2b18\n", "E RKNN: [11:45:59.274] REGTASK: The bit width of field value exceeds the limit, target: v2, offset: 0x5048, shift = 19, limit: 0x1fff, value: 0x3fe0\n", "E RKNN: [11:45:59.274] REGTASK: The bit width of field value exceeds the limit, target: v2, offset: 0x4038, shift = 0, limit: 0x1fff, value: 0x3fe0\n", "E RKNN: [11:45:59.274] REGTASK: The bit width of field value exceeds the limit, target: v2, offset: 0x4038, shift = 16, limit: 0x1fff, value: 0x3fe0\n", "E RKNN: [11:46:04.640] Unkown op target: 0\n", "E RKNN: [11:46:04.640] Unkown op target: 0\n", "I rknn building done.\n"]}, {"data": {"text/plain": ["0"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["from rknn.api import RKNN\n", "import numpy as np\n", "import os\n", "\n", "model_path = 'qwen2-vl-2b/qwen2_vl_2b_vision.onnx'\n", "target_platform = 'rk3588'\n", "\n", "rknn = RKNN(verbose=False)\n", "rknn.config(target_platform=target_platform, mean_values=[[0.48145466 * 255, 0.4578275 * 255, 0.40821073 * 255]], std_values=[[0.26862954 * 255, 0.26130258 * 255, 0.27577711 * 255]])\n", "rknn.load_onnx(model_path)\n", "rknn.build(do_quantization=False, dataset=None)\n", "os.makedirs(\"rknn\", exist_ok=True)\n", "rknn.export_rknn(\"./rknn/\" + os.path.splitext(os.path.basename(model_path))[0] + \"_{}.rknn\".format(target_platform))\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["inputs_embeds torch.Size([1, 181, 1536])\n", "inputs_embeds torch.Size([1, 387, 1536])\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 2/2 [00:00<00:00,  6.02it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Done\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["import torch\n", "import os\n", "import torchvision.transforms as T\n", "from torchvision.transforms.functional import InterpolationMode\n", "from PIL import Image\n", "import json\n", "import numpy as np\n", "from tqdm import tqdm\n", "from transformers import AutoModel, AutoTokenizer, AutoProcessor, Qwen2VLForConditionalGeneration\n", "\n", "path = '/home/<USER>/userSpace/studnet/home/<USER>/module_C/模块C/resource/LLaMA-Factory-0.9.1/ans/qwen2vl_lora_sft'\n", "model = Qwen2VLForConditionalGeneration.from_pretrained(\n", "    path, torch_dtype=\"auto\", device_map=\"cpu\",\n", "    low_cpu_mem_usage=True,\n", "    trust_remote_code=True).eval()\n", "\n", "processor = AutoProcessor.from_pretrained(path, size={\"shortest_edge\": 56 * 56, \"longest_edge\": 28 * 28 * 1280})\n", "\n", "datasets = json.load(open(\"resource/data/test.json\", 'r'))\n", "for data in datasets:\n", "    image_name = data[\"image\"].split(\".\")[0]\n", "    imgp = os.path.join(data[\"image_path\"], data[\"image\"])\n", "    image = Image.open(imgp)\n", "\n", "    conversation = [\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": [\n", "                {\n", "                    \"type\": \"image\",\n", "                },\n", "                {\"type\": \"text\", \"text\": data[\"input\"]},\n", "            ],\n", "        }\n", "    ]\n", "    text_prompt = processor.apply_chat_template(conversation, add_generation_prompt=True)\n", "    inputs = processor(\n", "        text=[text_prompt], images=[image], padding=True, return_tensors=\"pt\"\n", "    )\n", "    inputs = inputs.to(model.device)\n", "    inputs_embeds = model.model.embed_tokens(inputs[\"input_ids\"])\n", "    pixel_values = inputs[\"pixel_values\"].type(model.visual.get_dtype())\n", "    image_mask = inputs[\"input_ids\"] == model.config.image_token_id\n", "    image_embeds = model.visual(pixel_values, grid_thw=inputs[\"image_grid_thw\"]).to(inputs_embeds.device)\n", "    inputs_embeds[image_mask] = image_embeds\n", "    print(\"inputs_embeds\", inputs_embeds.shape)\n", "    os.makedirs(\"resource/data/inputs_embeds/\", exist_ok=True)\n", "    np.save(\"resource/data/inputs_embeds/{}\".format(image_name), inputs_embeds.to(dtype=torch.float16).cpu().detach().numpy())\n", "    \n", "with open('resource/data/inputs.json', 'w') as json_file:\n", "    json_file.write('[\\n')\n", "    first = True\n", "    for data in tqdm(datasets):\n", "        input_embed = np.load(os.path.join(\"resource/data/inputs_embeds\", data[\"image\"].split(\".\")[0]+'.npy'))\n", "        target = data[\"target\"]\n", "        input_dict = {\n", "            \"input_embed\": input_embed.tolist(),\n", "            \"target\": target\n", "        }\n", "        if not first:\n", "            json_file.write(',\\n')\n", "        else:\n", "            first = False\n", "        json.dump(input_dict, json_file)\n", "    json_file.write('\\n]')\n", "\n", "print(\"Done\")\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[37mINFO: rkllm-toolkit version: 1.2.1\u001b[0m\n", "Unrecognized keys in `rope_scaling` for 'rope_type'='default': {'mrope_section'}\n", "\u001b[33mWARNING: rkllm-toolkit only exports Qwen2ForCausalLM of Qwen2VLForConditionalGeneration!\u001b[0m\n", "Building model: 100%|██████████| 371/371 [00:04<00:00, 76.17it/s]\n", "Downloading data files: 100%|██████████| 1/1 [00:00<00:00, 14513.16it/s]\n", "Extracting data files: 100%|██████████| 1/1 [00:00<00:00, 797.24it/s]\n", "Generating train split: 2 examples [00:00, 16.35 examples/s]\n", "Optimizing model: 100%|██████████| 28/28 [06:11<00:00, 13.25s/it]\n", "\u001b[37mINFO: Setting chat_template to \"<|im_start|>system\\nYou are a helpful assistant.<|im_end|>\\n<|im_start|>user\\n[content]<|im_end|>\\n<|im_start|>assistant\\n\"\u001b[0m\n", "\u001b[37mINFO: Setting token_id of eos to 151645\u001b[0m\n", "\u001b[37mINFO: Setting token_id of pad to 151643\u001b[0m\n", "\u001b[37mINFO: Setting token_id of bos to 151643\u001b[0m\n", "Converting model: 100%|██████████| 339/339 [00:00<00:00, 5060032.23it/s]\n", "\u001b[37mINFO: Setting max_context_limit to 4096\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO: Exporting the model, please wait ....\n", "[=================================================>] 597/597 (100%)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[37mINFO: Model has been saved to qwen2_vl_2b_instruct.rkllm!\u001b[0m\n"]}], "source": ["import os\n", "import sys\n", "__builtins__.exit = sys.exit\n", "\n", "from rkllm.api import RKLLM\n", "from datasets import load_dataset\n", "from transformers import  AutoTokenizer\n", "from tqdm import tqdm\n", "import torch\n", "from torch import nn\n", "\n", "modelpath = '/home/<USER>/userSpace/studnet/home/<USER>/module_C/模块C/resource/LLaMA-Factory-0.9.1/ans/qwen2vl_lora_sft'\n", "target_platform = 'rk3588'\n", "num_npu_core = 3\n", "quantized_dtype = 'w8a8'\n", "device = 'cpu'\n", "savepath = 'qwen2_vl_2b_instruct.rkllm'\n", "llm = RKLLM()\n", "\n", "# Load model\n", "# Use 'export CUDA_VISIBLE_DEVICES=2' to specify GPU device\n", "ret = llm.load_huggingface(model=modelpath, device=device)\n", "if ret != 0:\n", "    print('Load model failed!')\n", "    exit(ret)\n", "\n", "# Build model\n", "dataset = 'resource/data/inputs.json'\n", "\n", "qparams = None\n", "ret = llm.build(do_quantization=True, optimization_level=1, quantized_dtype=quantized_dtype,\n", "                quantized_algorithm='normal', target_platform=target_platform, num_npu_core=num_npu_core, extra_qparams=qparams, dataset=dataset)\n", "\n", "if ret != 0:\n", "    print('Build model failed!')\n", "    exit(ret)\n", "\n", "# # Export rkllm model\n", "ret = llm.export_rkllm(savepath)\n", "if ret != 0:\n", "    print('Export model failed!')\n", "    exit(ret)\n", "\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 步骤2.板端测试"]}, {"cell_type": "markdown", "metadata": {}, "source": ["| Accuracy 范围         | 得分  |\n", "|----------------------|-------|\n", "| ≤ 0.60               | 0     |\n", "| 0.60 < Accuracy ≤ 0.65 | 1     |\n", "| 0.65 < Accuracy ≤ 0.70 | 1.5   |\n", "| 0.70 < Accuracy ≤ 0.75 | 2     |\n", "| 0.75 < Accuracy ≤ 0.80 | 2.5   |\n", "| 0.80 < Accuracy ≤ 0.85 | 3     |\n", "| > 0.85               | 3.5   |"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <11> 每题0.5分 共2.5分"]}], "metadata": {"kernelspec": {"display_name": "module_C_2_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 2}