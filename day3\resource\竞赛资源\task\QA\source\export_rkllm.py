import os
import sys
__builtins__.exit = sys.exit

from rkllm.api import RK<PERSON><PERSON>
from datasets import load_dataset
from transformers import  AutoTokenizer
from tqdm import tqdm
import torch
from torch import nn

# <3>加载微调导出后的模型路径
modelpath = _____
target_platform = 'rk3588'
num_npu_core = 3
# <4>设置量化类型为w8a8
quantized_dtype = _____
device = 'cpu'
savepath = 'qwen2_vl_2b_instruct.rkllm'
llm = RKLLM()

# Load model
# Use 'export CUDA_VISIBLE_DEVICES=2' to specify GPU device
ret = llm.load_huggingface(model=modelpath, device=device)
if ret != 0:
    print('Load model failed!')
    exit(ret)

# Build model
dataset = 'data/inputs.json'

qparams = None
ret = llm.build(do_quantization=True, optimization_level=1, quantized_dtype=quantized_dtype,
                quantized_algorithm='normal', target_platform=target_platform, num_npu_core=num_npu_core, extra_qparams=qparams, dataset=dataset)

if ret != 0:
    print('Build model failed!')
    exit(ret)

# # Export rkllm model
ret = llm.export_rkllm(savepath)
if ret != 0:
    print('Export model failed!')
    exit(ret)


